import { writable } from 'svelte/store';

export interface CartItem {
  id: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  category: string;
}

export interface CartStore {
  items: CartItem[];
  total: number;
  itemCount: number;
}

function createCartStore() {
  const { subscribe, set, update } = writable<CartStore>({
    items: [],
    total: 0,
    itemCount: 0
  });

  return {
    subscribe,
    addItem: (item: Omit<CartItem, 'quantity'>) =>
      update(store => {
        const existingItem = store.items.find(i => i.id === item.id);
        if (existingItem) {
          existingItem.quantity += 1;
        } else {
          store.items.push({ ...item, quantity: 1 });
        }
        store.total = store.items.reduce((sum, i) => sum + i.price * i.quantity, 0);
        store.itemCount = store.items.reduce((sum, i) => sum + i.quantity, 0);
        return store;
      }),
    removeItem: (id: string) =>
      update(store => {
        store.items = store.items.filter(i => i.id !== id);
        store.total = store.items.reduce((sum, i) => sum + i.price * i.quantity, 0);
        store.itemCount = store.items.reduce((sum, i) => sum + i.quantity, 0);
        return store;
      }),
    updateQuantity: (id: string, quantity: number) =>
      update(store => {
        const item = store.items.find(i => i.id === id);
        if (item) {
          if (quantity <= 0) {
            store.items = store.items.filter(i => i.id !== id);
          } else {
            item.quantity = quantity;
          }
        }
        store.total = store.items.reduce((sum, i) => sum + i.price * i.quantity, 0);
        store.itemCount = store.items.reduce((sum, i) => sum + i.quantity, 0);
        return store;
      }),
    clear: () => set({ items: [], total: 0, itemCount: 0 })
  };
}

export const cart = createCartStore();