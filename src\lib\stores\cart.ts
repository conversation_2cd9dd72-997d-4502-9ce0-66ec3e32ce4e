import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export interface CartItem {
  id: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  category: string;
}

export interface CartStore {
  items: CartItem[];
  total: number;
  itemCount: number;
  loading: boolean;
}

function createCartStore() {
  const { subscribe, set, update } = writable<CartStore>({
    items: [],
    total: 0,
    itemCount: 0,
    loading: false
  });

  // Load cart from server on initialization
  const loadCart = async () => {
    if (!browser) return;

    update(store => ({ ...store, loading: true }));

    try {
      const response = await fetch('/api/cart');
      if (response.ok) {
        const cartData = await response.json();
        set({ ...cartData, loading: false });
      }
    } catch (error) {
      console.error('Failed to load cart:', error);
      update(store => ({ ...store, loading: false }));
    }
  };

  // Initialize cart
  if (browser) {
    loadCart();
  }

  return {
    subscribe,
    loadCart,
    addItem: async (item: Omit<CartItem, 'quantity'>) => {
      if (!browser) return;

      try {
        const response = await fetch('/api/cart', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ productId: item.id, quantity: 1 })
        });

        if (response.ok) {
          await loadCart(); // Reload cart from server
        }
      } catch (error) {
        console.error('Failed to add item to cart:', error);
      }
    },
    removeItem: async (id: string) => {
      if (!browser) return;

      try {
        const response = await fetch(`/api/cart/${id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          await loadCart(); // Reload cart from server
        }
      } catch (error) {
        console.error('Failed to remove item from cart:', error);
      }
    },
    updateQuantity: async (id: string, quantity: number) => {
      if (!browser) return;

      try {
        const response = await fetch(`/api/cart/${id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ quantity })
        });

        if (response.ok) {
          await loadCart(); // Reload cart from server
        }
      } catch (error) {
        console.error('Failed to update cart item:', error);
      }
    },
    clear: async () => {
      if (!browser) return;

      // Clear all items by setting quantity to 0 for each
      const currentCart = await new Promise<CartStore>(resolve => {
        const unsubscribe = subscribe(cart => {
          unsubscribe();
          resolve(cart);
        });
      });

      for (const item of currentCart.items) {
        await fetch(`/api/cart/${item.id}`, {
          method: 'DELETE'
        });
      }

      await loadCart(); // Reload cart from server
    }
  };
}

export const cart = createCartStore();