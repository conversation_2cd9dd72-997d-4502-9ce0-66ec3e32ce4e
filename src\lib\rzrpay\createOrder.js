import Razorpay from "razorpay";
import crypto from "crypto";

const init = new Razorpay({
  key_id: key_id, // test keu
  key_secret: key_secret, // test key
});

const createOrder = async () => {
  try {
    const options = {
      amount: 50000, //  50000p = rs 500
      currency: "INR",
      receipt: "order_rcptid_11",
      payment_capture: 1, // Auto-capture after payment (1 = true)
    };

    const order = await init.orders.create(options);
    console.log("Order created:", order);
    return order;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
};

// createOrder();

const verifyPayment = (
  razorpayOrderId,
  razorpayPaymentId,
  razorpaySignature
) => {
  const generatedSignature = crypto
    .createHmac("sha256", key_secret)
    .update(razorpayOrderId + "|" + razorpayPaymentId)
    .digest("hex");

  if (generatedSignature === razorpaySignature) {
    console.log("Payment verified successfully");
    return true;
  } else {
    console.error("Payment verification failed");
    return false;
  }
};

// Example call to verify
const isPaymentValid = verifyPayment(
  "order_PQj44lDKiFB8XE",
  "pay_PQjTrnGswCPeDU",
  "d98c4523baf2665f256472f14255b5487f2262276369b5e130a142e0c07d208d"
);
