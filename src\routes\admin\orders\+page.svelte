<script lang="ts">
  import { onMount } from 'svelte';
  
  let orders: any[] = [];
  let loading = true;
  let selectedOrder: any = null;

  onMount(async () => {
    await loadOrders();
  });

  async function loadOrders() {
    try {
      const response = await fetch('/api/admin/orders');
      if (response.ok) {
        orders = await response.json();
      }
    } catch (error) {
      console.error('Failed to load orders:', error);
    } finally {
      loading = false;
    }
  }

  async function updateOrderStatus(orderId: string, status: string) {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        await loadOrders();
      } else {
        alert('Failed to update order status');
      }
    } catch (error) {
      console.error('Failed to update order status:', error);
      alert('Failed to update order status');
    }
  }

  function viewOrderDetails(order: any) {
    selectedOrder = order;
  }

  function closeOrderDetails() {
    selectedOrder = null;
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800';
      case 'PROCESSING': return 'bg-blue-100 text-blue-800';
      case 'SHIPPED': return 'bg-purple-100 text-purple-800';
      case 'DELIVERED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  }
</script>

<div class="px-4 py-6 sm:px-0">
  <div class="border-4 border-dashed border-gray-200 rounded-lg p-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">Orders</h1>

    {#if loading}
      <div class="text-center py-8">
        <div class="animate-spin w-8 h-8 border-2 border-pink-soft border-t-transparent rounded-full mx-auto"></div>
        <p class="mt-2 text-gray-600">Loading orders...</p>
      </div>
    {:else if orders.length === 0}
      <div class="text-center py-8">
        <p class="text-gray-500">No orders found.</p>
      </div>
    {:else}
      <!-- Orders Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {#each orders as order}
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {order.id.slice(0, 8)}...
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {order.user.email}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ₹{order.totalAmount.toFixed(2)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(order.status)}">
                    {order.status}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(order.createdAt).toLocaleDateString()}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    on:click={() => viewOrderDetails(order)}
                    class="text-indigo-600 hover:text-indigo-900 mr-3"
                  >
                    View
                  </button>
                  <select
                    value={order.status}
                    on:change={(e) => updateOrderStatus(order.id, e.target.value)}
                    class="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="PENDING">Pending</option>
                    <option value="PAID">Paid</option>
                    <option value="PROCESSING">Processing</option>
                    <option value="SHIPPED">Shipped</option>
                    <option value="DELIVERED">Delivered</option>
                    <option value="CANCELLED">Cancelled</option>
                  </select>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>
</div>

<!-- Order Details Modal -->
{#if selectedOrder}
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">Order Details</h3>
          <button
            on:click={closeOrderDetails}
            class="text-gray-400 hover:text-gray-600"
          >
            <span class="sr-only">Close</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <h4 class="font-medium text-gray-900">Order Information</h4>
            <p class="text-sm text-gray-600">Order ID: {selectedOrder.id}</p>
            <p class="text-sm text-gray-600">Date: {new Date(selectedOrder.createdAt).toLocaleString()}</p>
            <p class="text-sm text-gray-600">Status: {selectedOrder.status}</p>
            <p class="text-sm text-gray-600">Total: ₹{selectedOrder.totalAmount.toFixed(2)}</p>
          </div>

          <div>
            <h4 class="font-medium text-gray-900">Customer Information</h4>
            <p class="text-sm text-gray-600">Email: {selectedOrder.user.email}</p>
            {#if selectedOrder.shippingAddress}
              {@const address = JSON.parse(selectedOrder.shippingAddress)}
              <div class="text-sm text-gray-600">
                <p>Name: {address.firstName} {address.lastName}</p>
                <p>Address: {address.address}</p>
                <p>City: {address.city}, {address.state} {address.zipCode}</p>
                <p>Phone: {address.phone}</p>
              </div>
            {/if}
          </div>

          <div>
            <h4 class="font-medium text-gray-900">Order Items</h4>
            <div class="space-y-2">
              {#each selectedOrder.items as item}
                <div class="flex items-center space-x-3 p-2 border rounded">
                  <img src={item.product.image} alt={item.product.name} class="w-12 h-12 object-cover rounded" />
                  <div class="flex-1">
                    <p class="text-sm font-medium">{item.product.name}</p>
                    <p class="text-sm text-gray-600">Quantity: {item.quantity}</p>
                    <p class="text-sm text-gray-600">Price: ₹{item.pricePerUnit}</p>
                  </div>
                  <div class="text-sm font-medium">
                    ₹{(item.quantity * item.pricePerUnit).toFixed(2)}
                  </div>
                </div>
              {/each}
            </div>
          </div>

          {#if selectedOrder.razorpayPaymentId}
            <div>
              <h4 class="font-medium text-gray-900">Payment Information</h4>
              <p class="text-sm text-gray-600">Payment ID: {selectedOrder.razorpayPaymentId}</p>
              <p class="text-sm text-gray-600">Method: {selectedOrder.paymentMethod}</p>
            </div>
          {/if}
        </div>

        <div class="mt-6 flex justify-end">
          <button
            on:click={closeOrderDetails}
            class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
