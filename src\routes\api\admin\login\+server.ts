import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import { verifyPassword } from '$lib/server/auth/password';
import { signJWT } from '$lib/server/auth/jwt';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';

export const POST: RequestHandler = async ({ request, cookies }) => {
  const { email, password } = await request.json();

  if (!email || !password) {
    return json({ error: 'Email and password are required' }, { status: 400 });
  }

  const admin = await prisma.admin.findUnique({
    where: { email }
  });

  if (!admin) {
    return json({ error: 'Invalid credentials' }, { status: 401 });
  }

  const isValidPassword = await verifyPassword(password, admin.password);
  
  if (!isValidPassword) {
    return json({ error: 'Invalid credentials' }, { status: 401 });
  }

  const token = signJWT({
    adminId: admin.id,
    email: admin.email
  });

  cookies.set('admin_token', token, {
    path: '/',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    httpOnly: true,
    secure: false, // Set to true in production with HTTPS
    sameSite: 'strict'
  });

  return json({
    success: true,
    admin: {
      id: admin.id,
      email: admin.email,
      name: admin.name
    }
  });
};
