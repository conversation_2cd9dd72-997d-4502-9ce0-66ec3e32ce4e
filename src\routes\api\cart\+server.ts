import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ cookies }) => {
  const sessionId = cookies.get('session_id');
  
  if (!sessionId) {
    return json({ items: [], total: 0, itemCount: 0 });
  }

  // For now, we'll use session-based cart (no user authentication required)
  // In a real app, you'd get the user ID from authentication
  let user = await prisma.user.findFirst({
    where: { id: sessionId }
  });

  if (!user) {
    // Create a guest user for the session
    user = await prisma.user.create({
      data: {
        id: sessionId,
        email: `guest_${sessionId}@temp.com`,
        role: 'CUSTOMER'
      }
    });
  }

  const cart = await prisma.cart.findUnique({
    where: { userId: user.id },
    include: {
      items: {
        include: {
          product: true
        }
      }
    }
  });

  if (!cart) {
    return json({ items: [], total: 0, itemCount: 0 });
  }

  const items = cart.items.map(item => ({
    id: item.product.id,
    name: item.product.name,
    price: item.pricePerUnit,
    image: item.product.image,
    quantity: item.quantity,
    category: item.product.category
  }));

  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  return json({ items, total, itemCount });
};

export const POST: RequestHandler = async ({ request, cookies }) => {
  const { productId, quantity = 1 } = await request.json();
  
  let sessionId = cookies.get('session_id');
  
  if (!sessionId) {
    sessionId = crypto.randomUUID();
    cookies.set('session_id', sessionId, { 
      path: '/', 
      maxAge: 60 * 60 * 24 * 30, // 30 days
      httpOnly: true,
      secure: false // Set to true in production with HTTPS
    });
  }

  // Get or create user
  let user = await prisma.user.findFirst({
    where: { id: sessionId }
  });

  if (!user) {
    user = await prisma.user.create({
      data: {
        id: sessionId,
        email: `guest_${sessionId}@temp.com`,
        role: 'CUSTOMER'
      }
    });
  }

  // Get or create cart
  let cart = await prisma.cart.findUnique({
    where: { userId: user.id }
  });

  if (!cart) {
    cart = await prisma.cart.create({
      data: { userId: user.id }
    });
  }

  // Get product details
  const product = await prisma.product.findUnique({
    where: { id: productId }
  });

  if (!product) {
    return json({ error: 'Product not found' }, { status: 404 });
  }

  // Add or update cart item
  const existingItem = await prisma.cartItem.findUnique({
    where: {
      cartId_productId: {
        cartId: cart.id,
        productId: productId
      }
    }
  });

  if (existingItem) {
    await prisma.cartItem.update({
      where: { id: existingItem.id },
      data: { quantity: existingItem.quantity + quantity }
    });
  } else {
    await prisma.cartItem.create({
      data: {
        cartId: cart.id,
        productId: productId,
        quantity: quantity,
        pricePerUnit: product.price
      }
    });
  }

  return json({ success: true });
};
