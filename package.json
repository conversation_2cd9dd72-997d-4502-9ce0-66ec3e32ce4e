{"name": "jewelish", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "start": "node build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:seed": "tsx prisma/seed.ts"}, "devDependencies": {"@iconify/svelte": "^5.0.1", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/bcryptjs": "^3.0.0", "@types/eslint": "^9.6.0", "@types/jsonwebtoken": "^9.0.10", "eslint": "^9.0.0", "globals": "^15.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.1.13", "tsx": "^4.20.6", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^6.0.0"}, "dependencies": {"@prisma/client": "^6.16.2", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "lucide-svelte": "^0.544.0", "prisma": "^6.16.2", "razorpay": "^2.9.6"}, "type": "module"}