{"name": "jewelish", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "start": "node build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@iconify/svelte": "^5.0.1", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/eslint": "^9.6.0", "eslint": "^9.0.0", "globals": "^15.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.1.13", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^6.0.0"}, "dependencies": {"lucide-svelte": "^0.544.0"}, "type": "module"}