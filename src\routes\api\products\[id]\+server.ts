import { json, error } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ params }) => {
  const product = await prisma.product.findUnique({
    where: { id: params.id }
  });

  if (!product) {
    throw error(404, 'Product not found');
  }

  return json({
    ...product,
    gallery: JSON.parse(product.gallery)
  });
};

export const PUT: RequestHandler = async ({ params, request }) => {
  const data = await request.json();
  
  const product = await prisma.product.update({
    where: { id: params.id },
    data: {
      ...data,
      gallery: JSON.stringify(data.gallery || [])
    }
  });

  return json({
    ...product,
    gallery: JSON.parse(product.gallery)
  });
};

export const DELETE: RequestHandler = async ({ params }) => {
  await prisma.product.delete({
    where: { id: params.id }
  });

  return json({ success: true });
};
