import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import { verifyJWT } from '$lib/server/auth/jwt';
import type { RequestHandler } from './$types';

export const PUT: RequestHandler = async ({ params, request, cookies }) => {
  const token = cookies.get('admin_token');
  
  if (!token) {
    return json({ error: 'Not authenticated' }, { status: 401 });
  }

  const payload = verifyJWT(token);
  
  if (!payload) {
    return json({ error: 'Invalid token' }, { status: 401 });
  }

  const { status } = await request.json();

  const order = await prisma.order.update({
    where: { id: params.id },
    data: { status }
  });

  return json(order);
};
