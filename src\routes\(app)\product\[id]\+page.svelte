<script lang="ts">
  import { page } from '$app/stores';
  import { products } from '$lib/data/products';
  import { cart } from '$lib/stores/cart';
  import { ChevronLeft, ChevronRight } from 'lucide-svelte';
  
  $: product = products.find(p => p.id === $page.params.id);
  
  let currentImageIndex = 0;
  
  function nextImage() {
    if (product) {
      currentImageIndex = (currentImageIndex + 1) % product.gallery.length;
    }
  }
  
  function prevImage() {
    if (product) {
      currentImageIndex = currentImageIndex === 0 ? product.gallery.length - 1 : currentImageIndex - 1;
    }
  }
  
  function addToCart() {
    if (product) {
      cart.addItem({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
        category: product.category
      });
    }
  }
</script>

<svelte:head>
  <title>{product?.name || 'Product'} - Jewelish</title>
  <meta name="description" content={product?.description || 'Handmade jewelry piece from Jewelish'} />
</svelte:head>

{#if !product}
  <div class="container mx-auto px-4 py-20 text-center">
    <div class="butterfly-large mx-auto mb-6 opacity-30"></div>
    <h1 class="text-3xl font-elegant text-gray-600 mb-4">Product not found</h1>
    <a href="/shop" class="btn-primary">Back to Shop</a>
  </div>
{:else}
  <div class="container mx-auto px-4 py-12">
    <!-- Breadcrumb -->
    <nav class="mb-8">
      <div class="flex items-center space-x-2 text-sm text-gray-600">
        <a href="/" class="hover:text-pink-soft">Home</a>
        <span>/</span>
        <a href="/shop" class="hover:text-pink-soft">Shop</a>
        <span>/</span>
        <span class="text-pink-soft">{product.name}</span>
      </div>
    </nav>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Image Gallery -->
      <div class="relative">
        <div class="jewelry-box aspect-square mb-4">
          <img 
            src={product.gallery[currentImageIndex]} 
            alt={product.name}
            class="w-full h-full object-cover rounded-2xl"
          />
          
          {#if product.gallery.length > 1}
            <button 
              on:click={prevImage}
              class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-2 hover:bg-white transition-colors"
            >
              <ChevronLeft size={20} />
            </button>
            <button 
              on:click={nextImage}
              class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-2 hover:bg-white transition-colors"
            >
              <ChevronRight size={20} />
            </button>
          {/if}
          
          <!-- Decorative butterflies -->
          <div class="absolute top-4 right-4 butterfly animate-flutter opacity-60"></div>
        </div>
        
        {#if product.gallery.length > 1}
          <div class="flex space-x-2">
            {#each product.gallery as image, index}
              <button
                on:click={() => currentImageIndex = index}
                class="jewelry-box w-20 h-20 {index === currentImageIndex ? 'ring-2 ring-pink-soft' : ''}"
              >
                <img 
                  src={image} 
                  alt={product.name}
                  class="w-full h-full object-cover rounded-lg"
                />
              </button>
            {/each}
          </div>
        {/if}
      </div>
      
      <!-- Product Info -->
      <div class="space-y-6">
        <div>
          <span class="text-sm text-pink-soft uppercase tracking-wide font-medium">
            {product.category}
          </span>
          <h1 class="text-4xl font-elegant text-gray-800 mt-2">{product.name}</h1>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="butterfly animate-flutter opacity-50"></div>
          <div class="w-full h-px bg-gradient-to-r from-blush to-lavender-soft"></div>
          <div class="butterfly animate-float opacity-50"></div>
        </div>
        
        <p class="text-gray-600 text-lg leading-relaxed">{product.description}</p>
        
        <div class="space-y-3">
          <h3 class="font-elegant text-lg text-gray-800">Materials</h3>
          <p class="text-gray-600">{product.materials}</p>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="butterfly animate-flutter opacity-50"></div>
          <div class="w-full h-px bg-gradient-to-r from-blush to-lavender-soft"></div>
          <div class="butterfly animate-float opacity-50"></div>
        </div>
        
        <div class="flex items-center justify-between">
          <span class="text-3xl font-bold text-pink-soft">${product.price}</span>
          <button 
            on:click={addToCart}
            class="btn-primary text-lg px-8 py-4 hover:scale-105 transform transition-all group"
          >
            Add to Cart
            <span class="butterfly group-hover:animate-flutter ml-2 inline-block"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}