@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

 @layer base {
  body {
    font-family: 'Inter', sans-serif;
    background-color: #fff2e9;
  }
}

@layer components {
  /* .btn-primary {
    @apply bg-gradient-to-r from-blush to-pink-soft text-white px-6 py-3 rounded-full font-medium shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
  } */
  
  /* .btn-secondary {
    @apply bg-white text-pink-soft border-2 border-pink-soft px-6 py-3 rounded-full font-medium hover:bg-pink-soft hover:text-white transition-all duration-300;
  }
   */
  .card {
    @apply bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6;
  }
  
  /* .jewelry-box {
    @apply relative overflow-hidden rounded-2xl bg-gradient-to-br from-blush-light to-white shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-500;
  } */
} 

.main-heading {
  color: #7f6f6c;
  font-family: "Caveat", cursive;
  font-optical-sizing: auto;
  font-weight: 900;
  font-style: normal;
  
}

.butterfly {
  position: absolute;
  width: 25px;
  height: 25px;
  color: #ffa3dc;
  /* opacity: 0.2; */
  
  /* clip-path: polygon(
    50% 0%,
    80% 20%,
    100% 60%,
    75% 100%,
    50% 75%,
    25% 100%,
    0% 60%,
    20% 20%
  ); */
}

.butterfly-large {
  width: 30px;
  height: 30px;
}

.floating-element {
  animation: float 2s ease-in-out infinite;
}

.floating-element:nth-child(2n) {
  animation-delay: 2s;
}

.floating-element:nth-child(3n) {
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translatey(0);
  }
  50% {
    transform: translatey(-50px);
  }
}