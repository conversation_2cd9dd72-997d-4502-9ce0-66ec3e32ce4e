import crypto from "crypto";
import { SECRET_KEY } from "$env/static/private";
import type { RequestEvent } from "@sveltejs/kit";

export async function POST({ request }: RequestEvent) {
  const body = await request.text();
  const signature = request.headers.get("x-razorpay-signature");

  const expectedSignature = crypto
    .createHmac("sha256", SECRET_KEY)
    .update(body)
    .digest("hex");

  if (signature !== expectedSignature) {
    return new Response(JSON.stringify({ error: "Invalid signature" }), {
      status: 400,
    });
  }

  const event = JSON.parse(body);
  console.log("Webhook event:", event);

  return new Response("Webhook received", { status: 200 });
}
