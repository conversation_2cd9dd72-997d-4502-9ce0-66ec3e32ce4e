import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import type { RequestHandler } from './$types';

export const PUT: RequestHandler = async ({ params, request, cookies }) => {
  const { quantity } = await request.json();
  const sessionId = cookies.get('session_id');
  
  if (!sessionId) {
    return json({ error: 'No session found' }, { status: 400 });
  }

  const user = await prisma.user.findFirst({
    where: { id: sessionId }
  });

  if (!user) {
    return json({ error: 'User not found' }, { status: 404 });
  }

  const cart = await prisma.cart.findUnique({
    where: { userId: user.id }
  });

  if (!cart) {
    return json({ error: 'Cart not found' }, { status: 404 });
  }

  if (quantity <= 0) {
    // Remove item from cart
    await prisma.cartItem.deleteMany({
      where: {
        cartId: cart.id,
        productId: params.productId
      }
    });
  } else {
    // Update quantity
    await prisma.cartItem.updateMany({
      where: {
        cartId: cart.id,
        productId: params.productId
      },
      data: { quantity }
    });
  }

  return json({ success: true });
};

export const DELETE: RequestHandler = async ({ params, cookies }) => {
  const sessionId = cookies.get('session_id');
  
  if (!sessionId) {
    return json({ error: 'No session found' }, { status: 400 });
  }

  const user = await prisma.user.findFirst({
    where: { id: sessionId }
  });

  if (!user) {
    return json({ error: 'User not found' }, { status: 404 });
  }

  const cart = await prisma.cart.findUnique({
    where: { userId: user.id }
  });

  if (!cart) {
    return json({ error: 'Cart not found' }, { status: 404 });
  }

  await prisma.cartItem.deleteMany({
    where: {
      cartId: cart.id,
      productId: params.productId
    }
  });

  return json({ success: true });
};
