<script lang="ts">
  import { ShoppingBag, Menu, X } from "lucide-svelte";
  import { cart } from "$lib/stores/cart";
  import CartDrawer from "./CartDrawer.svelte";

  let mobileMenuOpen = false;
  let cartDrawerOpen = false;

  function toggleMobileMenu() {
    mobileMenuOpen = !mobileMenuOpen;
  }

  function openCart() {
    cartDrawerOpen = true;
  }
</script>

<header class="sticky top-0 z-50 bg-cream/95 backdrop-blur-sm shadow-sm">
  <nav class="container mx-auto px-4 py-4">
    <div class="flex items-center justify-between">
      <!-- Logo -->
      <a href="/" class="flex items-center space-x-2">
        <div class="jewelry-box w-12 h-12 flex items-center justify-center">
          <!-- <span class="text-2xl">💎</span> -->
          <img src="/logo.png" alt="" />
        </div>
        <!-- <h1 class="main-heading text-3xl font-cursive text-pink-soft">
          Jewelish
        </h1> -->
        <img class="main-heading w-20" src="/title.png" alt="Jewelish" />
      </a>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-8">
        <a href="/" class="text-gray-700 hover:text-pink-soft transition-colors"
          >Home</a
        >
        <a
          href="/shop"
          class="text-gray-700 hover:text-pink-soft transition-colors">Shop</a
        >
        <a
          href="/about"
          class="text-gray-700 hover:text-pink-soft transition-colors">About</a
        >
        <a
          href="/contact"
          class="text-gray-700 hover:text-pink-soft transition-colors"
          >Contact</a
        >
      </div>

      <!-- Cart & Mobile Menu -->
      <div class="flex items-center space-x-4">
        <button
          on:click={openCart}
          class="relative p-2 text-gray-700 hover:text-pink-soft transition-colors"
        >
          <ShoppingBag size={24} />
          {#if $cart.itemCount > 0}
            <span
              class="absolute -top-1 -right-1 bg-pink-soft text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
            >
              {$cart.itemCount}
            </span>
          {/if}
        </button>

        <!-- Mobile menu button -->
        <button
          on:click={toggleMobileMenu}
          class="md:hidden p-2 text-gray-700 hover:text-pink-soft transition-colors"
        >
          {#if mobileMenuOpen}
            <X size={24} />
          {:else}
            <Menu size={24} />
          {/if}
        </button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    {#if mobileMenuOpen}
      <div class="md:hidden mt-4 py-4 border-t border-blush-light">
        <div class="flex flex-col space-y-4">
          <a
            href="/"
            class="text-gray-700 hover:text-pink-soft transition-colors">Home</a
          >
          <a
            href="/shop"
            class="text-gray-700 hover:text-pink-soft transition-colors">Shop</a
          >
          <a
            href="/about"
            class="text-gray-700 hover:text-pink-soft transition-colors"
            >About</a
          >
          <a
            href="/contact"
            class="text-gray-700 hover:text-pink-soft transition-colors"
            >Contact</a
          >
        </div>
      </div>
    {/if}
  </nav>

  <!-- Floating butterflies -->
  <div
    class="absolute top-4 right-20 butterfly animate-flutter opacity-30"
  ></div>
  <div class="absolute top-8 right-32 butterfly animate-float opacity-20"></div>
</header>

<CartDrawer bind:open={cartDrawerOpen} />
