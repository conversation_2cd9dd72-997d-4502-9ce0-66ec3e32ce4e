<script lang="ts">
  import { page } from '$app/stores';
  import { products } from '$lib/data/products';
  import ProductCard from '$lib/components/ProductCard.svelte';
  
  $: selectedCategory = $page.url.searchParams.get('category');
  $: filteredProducts = selectedCategory 
    ? products.filter(p => p.category === selectedCategory)
    : products;
    
  const categories = [
    { id: 'all', name: 'All', emoji: '✨' },
    { id: 'rings', name: 'Rings', emoji: '💍' },
    { id: 'necklaces', name: 'Necklaces', emoji: '📿' },
    { id: 'bracelets', name: 'Bracelets', emoji: '🔗' },
    { id: 'earrings', name: 'Earrings', emoji: '👂' }
  ];
  
  function filterByCategory(category: string) {
    if (category === 'all') {
      window.location.href = '/shop';
    } else {
      window.location.href = `/shop?category=${category}`;
    }
  }
</script>

<svelte:head>
  <title>Shop - Jewelish</title>
  <meta name="description" content="Browse our complete collection of handmade jewelry including rings, necklaces, bracelets, and earrings." />
</svelte:head>

<div class="container mx-auto px-4 py-12">
  <!-- Header -->
  <div class="text-center mb-16">
    <h1 class="text-5xl font-elegant text-gray-800 mb-4">Our Collection</h1>
    <div class="flex justify-center items-center space-x-4 mb-8">
      <div class="butterfly animate-flutter opacity-40"></div>
      <p class="text-lg text-gray-600">Discover pieces made with love</p>
      <div class="butterfly animate-float opacity-40"></div>
    </div>
  </div>
  
  <!-- Category Filters -->
  <div class="mb-12">
    <div class="flex flex-wrap justify-center gap-4">
      {#each categories as category}
        <button
          on:click={() => filterByCategory(category.id)}
          class="flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 {
            (category.id === 'all' && !selectedCategory) || selectedCategory === category.id
              ? 'bg-pink-soft text-white shadow-lg transform scale-105'
              : 'bg-white text-gray-700 border border-pink-soft hover:bg-pink-soft hover:text-white hover:scale-105'
          }"
        >
          <span class="text-lg">{category.emoji}</span>
          <span class="font-medium">{category.name}</span>
        </button>
      {/each}
    </div>
  </div>
  
  <!-- Products Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
    {#each filteredProducts as product}
      <ProductCard {product} />
    {/each}
  </div>
  
  {#if filteredProducts.length === 0}
    <div class="text-center py-20">
      <div class="butterfly-large mx-auto mb-6 opacity-30"></div>
      <h3 class="text-2xl font-elegant text-gray-600 mb-4">No products found</h3>
      <p class="text-gray-500 mb-8">Try selecting a different category</p>
      <button 
        on:click={() => filterByCategory('all')}
        class="btn-primary"
      >
        View All Products
      </button>
    </div>
  {/if}
</div>