<script lang="ts">
  import { cart } from "$lib/stores/cart";
  import { goto } from "$app/navigation";

  let step = 1;
  let orderComplete = false;

  // Form data
  let shippingInfo = {
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    country: "US",
  };

  let paymentInfo = {
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    nameOnCard: "",
  };

  function nextStep() {
    step++;
  }

  function prevStep() {
    step--;
  }

  async function completeOrder() {
    try {
      // Create Razorpay order
      const response = await fetch("/api/razor/create-order", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ shippingAddress: shippingInfo }),
      });

      if (!response.ok) {
        throw new Error("Failed to create order");
      }

      const { order, key } = await response.json();

      // Initialize Razorpay payment
      const options = {
        key: key,
        amount: order.amount,
        currency: order.currency,
        name: "Jewelish",
        description: "Handmade Jewelry Purchase",
        order_id: order.id,
        handler: async function (response: any) {
          // Verify payment
          const verifyResponse = await fetch("/api/razor/verify-payment", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
            }),
          });

          if (verifyResponse.ok) {
            orderComplete = true;
            cart.clear();
            setTimeout(() => {
              goto("/");
            }, 5000);
          } else {
            alert("Payment verification failed. Please contact support.");
          }
        },
        prefill: {
          name: `${shippingInfo.firstName} ${shippingInfo.lastName}`,
          email: shippingInfo.email,
          contact: shippingInfo.phone,
        },
        theme: {
          color: "#ffa3dc",
        },
      };

      // @ts-ignore
      const rzp = new Razorpay(options);
      rzp.open();
    } catch (error) {
      console.error("Payment error:", error);
      alert("Failed to initiate payment. Please try again.");
    }
  }

  $: if ($cart.items.length === 0 && !orderComplete) {
    goto("/shop");
  }
</script>

<svelte:head>
  <title>Checkout - Jewelish</title>
</svelte:head>

<div class="container mx-auto px-4 py-12">
  {#if orderComplete}
    <!-- Order Complete -->
    <div class="text-center py-20">
      <div
        class="jewelry-box w-32 h-32 mx-auto flex items-center justify-center text-6xl mb-8 animate-bounce-gentle"
      >
        💎
      </div>
      <div class="butterfly-large mx-auto mb-6 animate-flutter"></div>
      <h1 class="text-4xl font-elegant text-pink-soft mb-4">Order Complete!</h1>
      <p class="text-lg text-gray-600 mb-8">
        Thank you for your order. Your beautiful jewelry is being prepared with
        love.
      </p>
      <div class="flex justify-center space-x-4">
        <div class="butterfly animate-flutter opacity-60"></div>
        <div class="butterfly animate-float opacity-50"></div>
        <div class="butterfly animate-flutter opacity-60"></div>
      </div>
      <p class="text-sm text-gray-500 mt-8">
        Redirecting to home in a few seconds...
      </p>
    </div>
  {:else}
    <!-- Progress Indicator -->
    <div class="mb-12">
      <div class="flex items-center justify-center space-x-4">
        {#each [1, 2, 3] as stepNumber}
          <div class="flex items-center">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center {step >=
              stepNumber
                ? 'bg-pink-soft text-white'
                : 'bg-gray-200 text-gray-500'}"
            >
              {stepNumber}
            </div>
            {#if stepNumber < 3}
              <div
                class="butterfly mx-4 {step > stepNumber
                  ? 'animate-flutter'
                  : 'opacity-30'}"
              ></div>
            {/if}
          </div>
        {/each}
      </div>
      <div class="flex justify-center space-x-20 mt-2 text-sm text-gray-600">
        <span class={step >= 1 ? "text-pink-soft font-medium" : ""}
          >Shipping</span
        >
        <span class={step >= 2 ? "text-pink-soft font-medium" : ""}
          >Payment</span
        >
        <span class={step >= 3 ? "text-pink-soft font-medium" : ""}>Review</span
        >
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Forms -->
      <div class="lg:col-span-2">
        {#if step === 1}
          <!-- Shipping Information -->
          <div class="card">
            <h2 class="text-2xl font-elegant text-gray-800 mb-6">
              Shipping Information
            </h2>

            <form class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    for="firstName"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >First Name</label
                  >
                  <input
                    type="text"
                    id="firstName"
                    bind:value={shippingInfo.firstName}
                    required
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
                <div>
                  <label
                    for="lastName"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Last Name</label
                  >
                  <input
                    type="text"
                    id="lastName"
                    bind:value={shippingInfo.lastName}
                    required
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Email</label
                  >
                  <input
                    type="email"
                    id="email"
                    bind:value={shippingInfo.email}
                    required
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
                <div>
                  <label
                    for="phone"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Phone</label
                  >
                  <input
                    type="tel"
                    id="phone"
                    bind:value={shippingInfo.phone}
                    required
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
              </div>

              <div>
                <label
                  for="address"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Address</label
                >
                <input
                  type="text"
                  id="address"
                  bind:value={shippingInfo.address}
                  required
                  class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label
                    for="city"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >City</label
                  >
                  <input
                    type="text"
                    id="city"
                    bind:value={shippingInfo.city}
                    required
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
                <div>
                  <label
                    for="state"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >State</label
                  >
                  <input
                    type="text"
                    id="state"
                    bind:value={shippingInfo.state}
                    required
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
                <div>
                  <label
                    for="zipCode"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >ZIP Code</label
                  >
                  <input
                    type="text"
                    id="zipCode"
                    bind:value={shippingInfo.zipCode}
                    required
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
              </div>

              <button
                type="button"
                on:click={nextStep}
                class="w-full btn-primary"
              >
                Continue to Payment
              </button>
            </form>
          </div>
        {:else if step === 2}
          <!-- Payment Information -->
          <div class="card">
            <h2 class="text-2xl font-elegant text-gray-800 mb-6">
              Payment Information
            </h2>

            <form class="space-y-6">
              <div>
                <label
                  for="cardNumber"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Card Number</label
                >
                <input
                  type="text"
                  id="cardNumber"
                  bind:value={paymentInfo.cardNumber}
                  required
                  placeholder="1234 5678 9012 3456"
                  class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label
                    for="expiryDate"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Expiry Date</label
                  >
                  <input
                    type="text"
                    id="expiryDate"
                    bind:value={paymentInfo.expiryDate}
                    required
                    placeholder="MM/YY"
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
                <div>
                  <label
                    for="cvv"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >CVV</label
                  >
                  <input
                    type="text"
                    id="cvv"
                    bind:value={paymentInfo.cvv}
                    required
                    placeholder="123"
                    class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                  />
                </div>
              </div>

              <div>
                <label
                  for="nameOnCard"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Name on Card</label
                >
                <input
                  type="text"
                  id="nameOnCard"
                  bind:value={paymentInfo.nameOnCard}
                  required
                  class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent bg-cream/50"
                />
              </div>

              <div class="flex space-x-4">
                <button
                  type="button"
                  on:click={prevStep}
                  class="flex-1 btn-secondary"
                >
                  Back
                </button>
                <button
                  type="button"
                  on:click={nextStep}
                  class="flex-1 btn-primary"
                >
                  Review Order
                </button>
              </div>
            </form>
          </div>
        {:else if step === 3}
          <!-- Order Review -->
          <div class="card">
            <h2 class="text-2xl font-elegant text-gray-800 mb-6">
              Review Your Order
            </h2>

            <div class="space-y-6">
              <div>
                <h3 class="font-elegant text-lg text-gray-800 mb-3">
                  Shipping Address
                </h3>
                <div class="bg-cream/50 rounded-lg p-4 text-gray-600">
                  <p>{shippingInfo.firstName} {shippingInfo.lastName}</p>
                  <p>{shippingInfo.address}</p>
                  <p>
                    {shippingInfo.city}, {shippingInfo.state}
                    {shippingInfo.zipCode}
                  </p>
                  <p>{shippingInfo.email}</p>
                </div>
              </div>

              <div>
                <h3 class="font-elegant text-lg text-gray-800 mb-3">
                  Payment Method
                </h3>
                <div class="bg-cream/50 rounded-lg p-4 text-gray-600">
                  <p>**** **** **** {paymentInfo.cardNumber.slice(-4)}</p>
                  <p>{paymentInfo.nameOnCard}</p>
                </div>
              </div>

              <div class="flex space-x-4">
                <button
                  type="button"
                  on:click={prevStep}
                  class="flex-1 btn-secondary"
                >
                  Back
                </button>
                <button
                  type="button"
                  on:click={completeOrder}
                  class="flex-1 btn-primary group"
                >
                  Complete Order
                  <span
                    class="butterfly group-hover:animate-flutter ml-2 inline-block"
                  ></span>
                </button>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <!-- Order Summary -->
      <div class="card h-fit">
        <h3 class="text-xl font-elegant text-gray-800 mb-4">Order Summary</h3>

        <div class="space-y-4 mb-6">
          {#each $cart.items as item}
            <div class="flex items-center space-x-3">
              <img
                src={item.image}
                alt={item.name}
                class="w-12 h-12 object-cover rounded-lg"
              />
              <div class="flex-1">
                <p class="font-medium text-gray-800 text-sm">{item.name}</p>
                <p class="text-pink-soft text-sm">Qty: {item.quantity}</p>
              </div>
              <p class="font-medium text-gray-800">
                ${(item.price * item.quantity).toFixed(2)}
              </p>
            </div>
          {/each}
        </div>

        <div class="space-y-2 border-t border-blush-light pt-4">
          <div class="flex justify-between">
            <span class="text-gray-600">Subtotal</span>
            <span class="text-gray-800">${$cart.total.toFixed(2)}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Shipping</span>
            <span class="text-gray-800">Free</span>
          </div>
          <div
            class="flex justify-between font-bold text-lg border-t border-blush-light pt-2"
          >
            <span class="text-gray-800">Total</span>
            <span class="text-pink-soft">${$cart.total.toFixed(2)}</span>
          </div>
        </div>

        <div class="mt-6 flex justify-center">
          <div class="butterfly animate-flutter opacity-40"></div>
        </div>
      </div>
    </div>
  {/if}
</div>
