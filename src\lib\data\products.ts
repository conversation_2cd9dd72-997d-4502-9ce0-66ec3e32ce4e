export interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  gallery: string[];
  description: string;
  materials: string;
  category: 'rings' | 'necklaces' | 'bracelets' | 'earrings';
  featured: boolean;
  stock?: number;
  createdAt?: string;
  updatedAt?: string;
}

// Fetch products from API
export async function getProducts(options?: { category?: string; featured?: boolean }): Promise<Product[]> {
  const params = new URLSearchParams();

  if (options?.category) {
    params.append('category', options.category);
  }

  if (options?.featured !== undefined) {
    params.append('featured', options.featured.toString());
  }

  const response = await fetch(`/api/products?${params}`);
  if (!response.ok) {
    throw new Error('Failed to fetch products');
  }

  return response.json();
}

export async function getProduct(id: string): Promise<Product> {
  const response = await fetch(`/api/products/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch product');
  }

  return response.json();
}

// Legacy export for backward compatibility - will be loaded from API
export let products: Product[] = [];