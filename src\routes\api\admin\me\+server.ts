import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import { verifyJWT } from '$lib/server/auth/jwt';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ cookies }) => {
  const token = cookies.get('admin_token');
  
  if (!token) {
    return json({ error: 'Not authenticated' }, { status: 401 });
  }

  const payload = verifyJWT(token);
  
  if (!payload) {
    return json({ error: 'Invalid token' }, { status: 401 });
  }

  const admin = await prisma.admin.findUnique({
    where: { id: payload.adminId },
    select: {
      id: true,
      email: true,
      name: true,
      createdAt: true
    }
  });

  if (!admin) {
    return json({ error: 'Admin not found' }, { status: 404 });
  }

  return json({ admin });
};
