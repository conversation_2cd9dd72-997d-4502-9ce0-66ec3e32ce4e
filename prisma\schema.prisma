// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  price       Float
  image       String
  gallery     String // JSON string of image URLs
  description String
  materials   String
  category    String
  featured    <PERSON><PERSON><PERSON>  @default(false)
  stock       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  cartItems  CartItem[]
  orderItems OrderItem[]

  @@map("products")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String?
  role      UserRole @default(CUSTOMER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  cart   Cart?
  orders Order[]

  @@map("users")
}

model Cart {
  id              String   @id @default(cuid())
  userId          String   @unique
  appliedCouponId String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user          User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items         CartItem[]
  appliedCoupon Coupon?    @relation(fields: [appliedCouponId], references: [id])

  @@map("carts")
}

model CartItem {
  id           String @id @default(cuid())
  cartId       String
  productId    String
  quantity     Int
  pricePerUnit Float

  // Relations
  cart    Cart    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([cartId, productId])
  @@map("cart_items")
}

model Order {
  id                String      @id @default(cuid())
  userId            String
  razorpayOrderId   String?     @unique
  razorpayPaymentId String?
  status            OrderStatus @default(PENDING)
  totalAmount       Float
  shippingAddress   String // JSON string
  paymentMethod     String?
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  user  User        @relation(fields: [userId], references: [id])
  items OrderItem[]

  @@map("orders")
}

model OrderItem {
  id           String @id @default(cuid())
  orderId      String
  productId    String
  quantity     Int
  pricePerUnit Float

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model Coupon {
  id          String     @id @default(cuid())
  code        String     @unique
  type        CouponType
  value       Float
  maxDiscount Float?
  isActive    Boolean    @default(true)
  expiresAt   DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  carts Cart[]

  @@map("coupons")
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

enum UserRole {
  CUSTOMER
  ADMIN
}

enum OrderStatus {
  PENDING
  PAID
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum CouponType {
  PERCENTAGE_DISCOUNT
  FIXED_AMOUNT
  BUY_ONE_GET_ONE
}
