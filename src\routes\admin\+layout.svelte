<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  
  let admin: any = null;
  let loading = true;

  onMount(async () => {
    try {
      const response = await fetch('/api/admin/me');
      if (response.ok) {
        const data = await response.json();
        admin = data.admin;
      } else {
        if ($page.url.pathname !== '/admin/login') {
          goto('/admin/login');
        }
      }
    } catch (error) {
      console.error('Failed to check admin auth:', error);
      if ($page.url.pathname !== '/admin/login') {
        goto('/admin/login');
      }
    } finally {
      loading = false;
    }
  });

  async function logout() {
    try {
      await fetch('/api/admin/logout', { method: 'POST' });
      goto('/admin/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }
</script>

<svelte:head>
  <title>Admin Dashboard - Jewelish</title>
</svelte:head>

{#if loading}
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="animate-spin w-8 h-8 border-2 border-pink-soft border-t-transparent rounded-full"></div>
  </div>
{:else if admin || $page.url.pathname === '/admin/login'}
  <div class="min-h-screen bg-gray-100">
    {#if admin}
      <!-- Admin Navigation -->
      <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <h1 class="text-xl font-semibold text-gray-900">Jewelish Admin</h1>
              <div class="ml-10 flex space-x-8">
                <a href="/admin" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                  Dashboard
                </a>
                <a href="/admin/products" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                  Products
                </a>
                <a href="/admin/orders" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                  Orders
                </a>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-700">Welcome, {admin.name}</span>
              <button 
                on:click={logout}
                class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>
    {/if}

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <slot />
    </main>
  </div>
{:else}
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="text-center">
      <h1 class="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
      <p class="text-gray-600 mb-4">You need to be logged in as an admin to access this page.</p>
      <a href="/admin/login" class="bg-pink-soft text-white px-4 py-2 rounded hover:bg-pink-600">
        Go to Login
      </a>
    </div>
  </div>
{/if}
