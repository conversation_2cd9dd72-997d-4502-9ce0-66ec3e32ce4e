import { prisma } from '../database/db';

export async function createOrder(userId: string, razorpayOrderId: string) {
  // Get user's cart
  const cart = await prisma.cart.findUnique({
    where: { userId },
    include: {
      items: {
        include: {
          product: true
        }
      },
      appliedCoupon: true
    }
  });

  if (!cart || cart.items.length === 0) {
    throw new Error('Cart is empty');
  }

  // Calculate total amount
  const subtotal = cart.items.reduce((acc, item) => acc + item.pricePerUnit * item.quantity, 0);
  
  let discountAmount = 0;
  if (cart.appliedCoupon) {
    const coupon = cart.appliedCoupon;
    if (coupon.type === 'PERCENTAGE_DISCOUNT') {
      discountAmount = (subtotal * coupon.value) / 100;
      if (coupon.maxDiscount && discountAmount > coupon.maxDiscount) {
        discountAmount = coupon.maxDiscount;
      }
    } else if (coupon.type === 'FIXED_AMOUNT') {
      discountAmount = Math.min(coupon.value, subtotal);
    } else if (coupon.type === 'BUY_ONE_GET_ONE') {
      const sortedItems = [...cart.items].sort((a, b) => a.pricePerUnit - b.pricePerUnit);
      if (sortedItems.length >= 2) {
        discountAmount = sortedItems[0].pricePerUnit;
      }
    }
  }

  const totalAmount = Math.max(0, subtotal - discountAmount);

  // Create order
  const order = await prisma.order.create({
    data: {
      userId,
      razorpayOrderId,
      totalAmount,
      shippingAddress: JSON.stringify({}), // Will be updated when payment is confirmed
      status: 'PENDING',
      items: {
        create: cart.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          pricePerUnit: item.pricePerUnit
        }))
      }
    },
    include: {
      items: {
        include: {
          product: true
        }
      }
    }
  });

  return order;
}
