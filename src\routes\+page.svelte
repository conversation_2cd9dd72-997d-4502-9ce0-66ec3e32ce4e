<script lang="ts">
  import { products } from "$lib/data/products";
  import ProductCard from "$lib/components/ProductCard.svelte";
  import Icon from "@iconify/svelte";

  const featuredProducts = products.filter((p) => p.featured);
</script>

<svelte:head>
  <title>Jewelish - Handmade Jewelry Inspired by Dreams</title>
  <meta
    name="description"
    content="Discover handcrafted jewelry pieces made with love and inspired by dreams. Featuring rings, necklaces, bracelets, and earrings."
  />
</svelte:head>

<!-- Hero Section -->
<section
  class="relative py-20 bg-gradient-to-br from-cream to-blush-light overflow-hidden"
>
  <div
    class="container mx-auto px-4 text-center flex justify-center items-center flex-col relative gap-4 z-10"
  >
    <!-- Animated Logo -->
    <div class="group cursor-pointer inline-block">
      <!-- <div
        class="jewelry-box w-32 h-32 mx-auto flex items-center justify-center text-6xl group-hover:scale-110 transition-transform duration-500"
      >
        💎
      </div> -->
      <img
        class="jewelry-box w-52 h-52 mx-auto flex items-center justify-center text-6xl group-hover:scale-110 transition-transform duration-500"
        src="/logo.png"
        alt=""
      />
    </div>

    <!-- <h1
      class="main-heading text-5xl md:text-7xl font-cursive text-pink-soft mb-6 animate-bounce-gentle"
    >
      Jewelish
    </h1> -->
    <img class="main-heading h-16" src="/title.png" alt="Jewelish" />

    <p class="text-xl text-gray-700 max-w-2xl mx-auto">
      Handmade with love, inspired by dreams
    </p>

    <a
      href="/shop"
      class="btn-primary text-lg inline-flex items-center space-x-2 group"
    >
      <span>Shop Now</span>
    </a>
  </div>
</section>

<!-- Featured Products -->
<section class="py-20">
  <div class="container mx-auto px-4">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-elegant text-gray-800 mb-4">
        Featured Collection
      </h2>
      <div class="flex justify-center items-center space-x-4 mb-8">
        <div class="butterfly animate-flutter opacity-40"></div>
        <p class="text-lg text-gray-600">Our most beloved pieces</p>
        <div class="butterfly animate-float opacity-40"></div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {#each featuredProducts as product}
        <ProductCard {product} />
      {/each}
    </div>

    <div class="text-center mt-12">
      <a href="/shop" class="btn-secondary"> View All Products </a>
    </div>
  </div>
</section>

<!-- About Preview -->
<section class="py-20 bg-gradient-to-r from-blush-light to-lavender-soft/30">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div class="relative">
        <img
          src="https://media.istockphoto.com/photos/jewelry-making-female-hands-with-a-tool-on-a-yellow-background-picture-id1081577556?k=20&m=1081577556&s=612x612&w=0&h=oBCl5orbH_z6nwkDJQesfl5023AG2mjC3dNlN9_zf8Y="
          alt="Handmade jewelry process"
          class="rounded-2xl shadow-xl"
        />
        <div
          class="absolute top-4 right-4 butterfly-large animate-flutter opacity-60"
        ></div>
        <div
          class="absolute bottom-8 left-4 butterfly animate-float opacity-50"
        ></div>
      </div>

      <div class="space-y-6">
        <h2 class="text-4xl font-elegant text-gray-800">Our Story</h2>
        <p class="text-gray-600 text-lg leading-relaxed">
          Every piece at Jewelish is carefully handcrafted with love and
          attention to detail. We believe that jewelry should tell a story and
          capture the magic of dreams.
        </p>
        <p class="text-gray-600 leading-relaxed">
          Using only the finest materials and traditional techniques, we create
          unique pieces that celebrate life's beautiful moments.
        </p>
        <a href="/about" class="btn-secondary inline-block"> Learn More </a>
      </div>
    </div>
  </div>
</section>
