<script lang="ts">
  import type { Product } from '$lib/data/products';
  import { cart } from '$lib/stores/cart';
  
  export let product: Product;
  
  function addToCart() {
    cart.addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      category: product.category
    });
  }
</script>

<div class="jewelry-box group cursor-pointer">
  <a href="/product/{product.id}" class="block">
    <div class="aspect-square overflow-hidden rounded-t-2xl">
      <img 
        src={product.image} 
        alt={product.name}
        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
      />
    </div>
  </a>
  
  <div class="p-6">
    <div class="absolute top-2 right-2 butterfly animate-flutter opacity-0 group-hover:opacity-60 transition-opacity"></div>
    
    <a href="/product/{product.id}">
      <h3 class="font-elegant text-lg text-gray-800 group-hover:text-pink-soft transition-colors mb-2">
        {product.name}
      </h3>
    </a>
    
    <p class="text-gray-600 text-sm mb-4 line-clamp-2">{product.description}</p>
    
    <div class="flex items-center justify-between">
      <span class="text-xl font-bold text-pink-soft">${product.price}</span>
      <button 
        on:click={addToCart}
        class="btn-secondary text-sm px-4 py-2 hover:scale-105 transform transition-all"
      >
        Add to Cart
      </button>
    </div>
  </div>
  
  <!-- Hover effect overlay -->
  <div class="absolute inset-0 bg-gradient-to-t from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none"></div>
</div>