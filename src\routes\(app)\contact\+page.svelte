<script lang="ts">
  let name = '';
  let email = '';
  let message = '';
  let submitted = false;
  
  function handleSubmit() {
    // In a real app, this would send the form data to a server
    submitted = true;
    setTimeout(() => {
      submitted = false;
      name = '';
      email = '';
      message = '';
    }, 3000);
  }
</script>

<svelte:head>
  <title>Contact - Jewelish</title>
  <meta name="description" content="Get in touch with Jewelish. We'd love to hear from you about our handmade jewelry collection." />
</svelte:head>

<div class="container mx-auto px-4 py-12">
  <!-- Header -->
  <div class="text-center mb-16">
    <h1 class="text-5xl font-elegant text-gray-800 mb-6">Get in Touch</h1>
    <div class="flex justify-center items-center space-x-4 mb-8">
      <div class="butterfly animate-flutter opacity-40"></div>
      <p class="text-xl text-gray-600 max-w-2xl">
        We'd love to hear from you about our dreamy jewelry collection
      </p>
      <div class="butterfly animate-float opacity-40"></div>
    </div>
  </div>
  
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
    <!-- Contact Form -->
    <div class="card relative overflow-hidden">
      <div class="absolute top-4 right-4 butterfly animate-flutter opacity-30"></div>
      <div class="absolute bottom-6 left-4 butterfly animate-float opacity-20"></div>
      
      <h2 class="text-2xl font-elegant text-gray-800 mb-6">Send us a Message</h2>
      
      {#if submitted}
        <div class="text-center py-8">
          <div class="butterfly-large mx-auto mb-4 animate-bounce-gentle"></div>
          <h3 class="text-xl font-elegant text-pink-soft mb-2">Message Sent!</h3>
          <p class="text-gray-600">Thank you for reaching out. We'll get back to you soon!</p>
        </div>
      {:else}
        <form on:submit|preventDefault={handleSubmit} class="space-y-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
            <input
              type="text"
              id="name"
              bind:value={name}
              required
              class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent transition-all bg-cream/50"
              placeholder="Your name"
            />
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              type="email"
              id="email"
              bind:value={email}
              required
              class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent transition-all bg-cream/50"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
            <textarea
              id="message"
              bind:value={message}
              required
              rows="6"
              class="w-full px-4 py-3 border border-blush rounded-lg focus:ring-2 focus:ring-pink-soft focus:border-transparent transition-all bg-cream/50 resize-none"
              placeholder="Tell us about your dream piece..."
            ></textarea>
          </div>
          
          <button type="submit" class="w-full btn-primary group">
            Send Message
            <span class="butterfly group-hover:animate-flutter ml-2 inline-block"></span>
          </button>
        </form>
      {/if}
    </div>
    
    <!-- Contact Info -->
    <div class="space-y-8">
      <div class="card">
        <h3 class="text-xl font-elegant text-gray-800 mb-4">Visit Our Workshop</h3>
        <div class="space-y-3 text-gray-600">
          <p>123 Dreamcatcher Lane</p>
          <p>Whimsical Gardens, WG 12345</p>
          <p>Open by appointment</p>
        </div>
      </div>
      
      <div class="card">
        <h3 class="text-xl font-elegant text-gray-800 mb-4">Get in Touch</h3>
        <div class="space-y-3 text-gray-600">
          <p>📧 <EMAIL></p>
          <p>📱 +****************</p>
          <p>💬 Follow your dreams</p>
        </div>
      </div>
      
      <div class="card bg-gradient-to-br from-blush-light to-lavender-soft/30">
        <h3 class="text-xl font-elegant text-gray-800 mb-4">Custom Orders</h3>
        <p class="text-gray-600 mb-4">
          Have a special piece in mind? We love bringing custom jewelry dreams to life. 
          Contact us to discuss your vision!
        </p>
        <div class="flex space-x-2">
          <div class="butterfly animate-flutter opacity-50"></div>
          <div class="butterfly animate-float opacity-40"></div>
        </div>
      </div>
    </div>
  </div>
</div>