import { prisma } from "$lib/server/database/db";
import <PERSON><PERSON><PERSON><PERSON> from "razorpay";
import { SECRET_KEY } from "$env/static/private";
import { PUBLIC_RAZORPAY_KEY_ID } from "$env/static/public";
import type { RequestEvent } from "@sveltejs/kit";
import { error, json } from "@sveltejs/kit";
import { createOrder } from "$lib/server/order/create";

const razorpay = new Razorpay({
  key_id: PUBLIC_RAZORPAY_KEY_ID,
  key_secret: SECRET_KEY,
});

export async function POST({ request, cookies }: RequestEvent) {
  const sessionId = cookies.get('session_id');
  const { shippingAddress } = await request.json();

  if (!sessionId) {
    throw error(401, "No session found");
  }

  // Get user by session ID
  const user = await prisma.user.findFirst({
    where: { id: sessionId },
    include: {
      cart: {
        include: {
          items: {
            include: {
              product: true
            }
          },
          appliedCoupon: true,
        },
      },
    },
  });

  if (!user || !user.cart || user.cart.items.length === 0) {
    throw error(400, "Cart is empty");
  }

  // Calculate subtotal
  const subtotal = user.cart.items.reduce((acc, item) => acc + item.pricePerUnit * item.quantity, 0);

  // Calculate discount if a coupon is applied
  let discountAmount = 0;
  if (user.cart.appliedCouponId) {
    const coupon = user.cart.appliedCoupon;

    if (coupon.type === "PERCENTAGE_DISCOUNT") {
      discountAmount = (subtotal * coupon.value) / 100;
      if (coupon.maxDiscount && discountAmount > coupon.maxDiscount) {
        discountAmount = coupon.maxDiscount;
      }
    } else if (coupon.type === "FIXED_AMOUNT") {
      discountAmount = Math.min(coupon.value, subtotal);
    } else if (coupon.type === "BUY_ONE_GET_ONE") {
      const sortedItems = [...user.cart.items].sort((a, b) => a.pricePerUnit - b.pricePerUnit);
      if (sortedItems.length >= 2) {
        discountAmount = sortedItems[0].pricePerUnit;
      }
    }
  }

  // Calculate final amount after discount
  const finalAmount = Math.max(0, subtotal - discountAmount);

  try {
    const order = await razorpay.orders.create({
      amount: Math.round(finalAmount * 100), // Amount in paise
      currency: "INR",
      receipt: `order_${Date.now()}`,
    });

    // Create local order record
    const orderLocal = await createOrder(user.id, order.id);

    // Update order with shipping address
    await prisma.order.update({
      where: { id: orderLocal.id },
      data: { shippingAddress: JSON.stringify(shippingAddress) }
    });

    return json({
      order: {
        id: order.id,
        amount: order.amount,
        currency: order.currency
      },
      key: PUBLIC_RAZORPAY_KEY_ID
    });
  } catch (err: any) {
    console.error("Error creating order:", err);
    throw error(500, err.message || "Failed to create order");
  }
}
