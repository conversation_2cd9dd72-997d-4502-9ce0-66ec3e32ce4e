import { prisma as database } from "$lib/server/database/db";
import <PERSON><PERSON><PERSON><PERSON> from "razorpay";
import { SECRET_KEY } from "$env/static/private"; // Ensure your keys are stored securely
import { PUBLIC_RAZORPAY_KEY_ID } from "$env/static/public";
import type { RequestEvent } from "@sveltejs/kit";
import { error } from "@sveltejs/kit";
import { createOrder } from "$lib/server/order/create";

const razorpay = new Razorpay({
  key_id: PUBLIC_RAZORPAY_KEY_ID,
  key_secret: SECRET_KEY,
});

export async function POST({ request, locals }: RequestEvent) {
  const userId = locals.userId;

  if (!userId || userId === -1) {
    throw error(401, "User not found");
  }

  const user = await database.user.findFirst({
    where: {
      id: userId,
    },
    include: {
      cart: {
        include: {
          items: true,
          appliedCoupon: true,
        },
      },
    },
  });

  // Calculate subtotal
  const subtotal = user.cart.items.reduce((acc, item) => acc + item.pricePerUnit * item.quantity, 0);

  // Calculate discount if a coupon is applied
  let discountAmount = 0;
  if (user.cart.appliedCouponId) {
    const coupon = user.cart.appliedCoupon;

    // Simple discount calculation for Razorpay order creation
    // The full calculation will be done in the createOrder function
    if (coupon.type === "PERCENTAGE_DISCOUNT") {
      discountAmount = (subtotal * coupon.value) / 100;
      if (coupon.maxDiscount && discountAmount > coupon.maxDiscount) {
        discountAmount = coupon.maxDiscount;
      }
    } else if (coupon.type === "FIXED_AMOUNT") {
      discountAmount = Math.min(coupon.value, subtotal);
    } else if (coupon.type === "BUY_ONE_GET_ONE") {
      // Simplified calculation for Razorpay
      const sortedItems = [...user.cart.items].sort((a, b) => a.pricePerUnit - b.pricePerUnit);
      if (sortedItems.length >= 2) {
        discountAmount = sortedItems[0].pricePerUnit;
      }
    }
  }

  // Calculate final amount after discount
  const finalAmount = Math.max(0, subtotal - discountAmount);
  console.log("subtotal", subtotal, "discount", discountAmount, "final", finalAmount);

  console.log("razorpay", {
    key_id: PUBLIC_RAZORPAY_KEY_ID,
    key_secret: SECRET_KEY,
  });

  try {
    const order = await razorpay.orders.create({
      amount: Math.round(finalAmount * 100), // Amount in paise (smallest currency unit)
      currency: "INR",
    });

    const orderLocal = await createOrder(userId, order.id);

    return new Response(JSON.stringify({ order }), { status: 200 });
  } catch (error) {
    console.error("Error creating order:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
    });
  }
}
