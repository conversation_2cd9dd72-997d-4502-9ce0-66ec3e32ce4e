import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
  const category = url.searchParams.get('category');
  const featured = url.searchParams.get('featured');
  
  const where: any = {};
  
  if (category) {
    where.category = category;
  }
  
  if (featured === 'true') {
    where.featured = true;
  }

  const products = await prisma.product.findMany({
    where,
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Parse gallery JSON strings
  const productsWithParsedGallery = products.map(product => ({
    ...product,
    gallery: JSON.parse(product.gallery)
  }));

  return json(productsWithParsedGallery);
};

export const POST: RequestHandler = async ({ request }) => {
  const data = await request.json();
  
  const product = await prisma.product.create({
    data: {
      ...data,
      gallery: JSON.stringify(data.gallery || [])
    }
  });

  return json({
    ...product,
    gallery: JSON.parse(product.gallery)
  });
};
