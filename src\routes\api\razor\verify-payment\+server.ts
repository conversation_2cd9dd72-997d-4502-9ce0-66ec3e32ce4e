import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import { SECRET_KEY } from '$env/static/private';
import crypto from 'crypto';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, cookies }) => {
  const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = await request.json();
  const sessionId = cookies.get('session_id');

  if (!sessionId) {
    return json({ error: 'No session found' }, { status: 400 });
  }

  // Verify signature
  const generatedSignature = crypto
    .createHmac('sha256', SECRET_KEY)
    .update(razorpay_order_id + '|' + razorpay_payment_id)
    .digest('hex');

  if (generatedSignature !== razorpay_signature) {
    return json({ error: 'Payment verification failed' }, { status: 400 });
  }

  try {
    // Update order status
    const order = await prisma.order.update({
      where: { razorpayOrderId: razorpay_order_id },
      data: {
        razorpayPaymentId: razorpay_payment_id,
        status: 'PAID',
        paymentMethod: 'Razorpay'
      }
    });

    // Clear the user's cart
    const user = await prisma.user.findFirst({
      where: { id: sessionId },
      include: { cart: true }
    });

    if (user?.cart) {
      await prisma.cartItem.deleteMany({
        where: { cartId: user.cart.id }
      });
    }

    return json({ 
      success: true, 
      orderId: order.id,
      message: 'Payment verified successfully' 
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    return json({ error: 'Failed to verify payment' }, { status: 500 });
  }
};
