<script lang="ts">
  import { cart } from "$lib/stores/cart";
  import { X, Plus, Minus } from "lucide-svelte";
  import { goto } from "$app/navigation";

  export let open = false;

  function closeDrawer() {
    open = false;
  }

  function proceedToCheckout() {
    closeDrawer();
    goto("/checkout");
  }
</script>

{#if open}
  <div class="fixed inset-0 z-50 overflow-hidden">
    <!-- Backdrop -->
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_static_element_interactions -->
    <div
      class="absolute inset-0 bg-black opacity-50 transition-opacity"
      on:click={closeDrawer}
    ></div>

    <!-- Drawer -->
    <div
      class="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl"
    >
      <div class="flex h-full flex-col">
        <!-- Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-200"
        >
          <h2 class="text-xl font-elegant">Shopping Cart</h2>
          <button
            on:click={closeDrawer}
            class="p-2 hover:bg-gray-100 rounded-full"
          >
            <X size={20} />
          </button>
        </div>

        <!-- Cart Items -->
        <div class="flex-1 overflow-y-auto p-6">
          {#if $cart.items.length === 0}
            <div class="text-center py-12">
              <div class="butterfly-large mx-auto mb-4 opacity-30"></div>
              <p class="text-gray-500">Your cart is empty</p>
            </div>
          {:else}
            <div class="space-y-4">
              {#each $cart.items as item}
                <div
                  class="flex items-center space-x-4 p-4 bg-cream rounded-xl"
                >
                  <img
                    src={item.image}
                    alt={item.name}
                    class="w-16 h-16 object-cover rounded-lg"
                  />
                  <div class="flex-1">
                    <h3 class="font-medium text-gray-800">{item.name}</h3>
                    <p class="text-pink-soft">${item.price}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <button
                      on:click={() =>
                        cart.updateQuantity(item.id, item.quantity - 1)}
                      class="p-1 hover:bg-white rounded"
                    >
                      <Minus size={16} />
                    </button>
                    <span class="w-8 text-center">{item.quantity}</span>
                    <button
                      on:click={() =>
                        cart.updateQuantity(item.id, item.quantity + 1)}
                      class="p-1 hover:bg-white rounded"
                    >
                      <Plus size={16} />
                    </button>
                  </div>
                  <button
                    on:click={() => cart.removeItem(item.id)}
                    class="text-red-400 hover:text-red-600"
                  >
                    <X size={16} />
                  </button>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <!-- Footer -->
        {#if $cart.items.length > 0}
          <div class="border-t border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="text-lg font-medium">Total:</span>
              <span class="text-xl font-bold text-pink-soft"
                >${$cart.total.toFixed(2)}</span
              >
            </div>
            <button on:click={proceedToCheckout} class="w-full btn-primary">
              Checkout
            </button>
          </div>
        {/if}
      </div>

      <!-- Decorative butterflies -->
      <div
        class="absolute top-16 left-4 butterfly animate-flutter opacity-20"
      ></div>
      <div
        class="absolute bottom-20 right-8 butterfly animate-float opacity-15"
      ></div>
    </div>
  </div>
{/if}
