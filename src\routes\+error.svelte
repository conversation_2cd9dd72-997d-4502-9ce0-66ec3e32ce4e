<script lang="ts">
  import { page } from "$app/stores";

  $: error = $page.error;
</script>

<svelte:head>
  <title>Oops! - Jewelish</title>
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-cream to-blush-light flex items-center justify-center relative overflow-hidden"
>
  <!-- Floating jewelry box -->
  <div
    class="absolute top-20 left-1/2 transform -translate-x-1/2 jewelry-box w-40 h-40 flex items-center justify-center text-8xl animate-float"
  >
    💎
  </div>

  <!-- Background butterflies -->
  <div
    class="absolute top-1/4 left-1/4 butterfly-large animate-flutter opacity-30"
  ></div>
  <div
    class="absolute top-1/3 right-1/4 butterfly animate-float opacity-40"
  ></div>
  <div
    class="absolute bottom-1/3 left-1/3 butterfly-large animate-flutter opacity-25"
  ></div>
  <div
    class="absolute bottom-1/4 right-1/3 butterfly animate-float opacity-35"
  ></div>

  <!-- Content -->
  <div class="text-center relative z-10 mt-20">
    <h1 class="text-8xl font-cursive text-pink-soft mb-4 animate-bounce-gentle">
      Oops!
    </h1>

    <div class="flex justify-center items-center space-x-4 mb-8">
      <div class="butterfly animate-flutter"></div>
      <h2 class="text-2xl font-elegant text-gray-700">
        {#if error?.status === 404}
          Page not found
        {:else}
          Something went wrong
        {/if}
      </h2>
      <div class="butterfly animate-float"></div>
    </div>

    <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
      {#if error?.status === 404}
        The page you're looking for has fluttered away like a butterfly in the
        wind.
      {:else}
        Don't worry, our magical butterflies will guide you back home.
      {/if}
    </p>

    <!-- Guiding butterflies -->
    <div class="mb-8">
      <div
        class="butterfly-large mx-auto animate-flutter opacity-60 mb-2"
      ></div>
      <div class="flex justify-center space-x-8">
        <div class="butterfly animate-float opacity-50"></div>
        <div class="butterfly animate-flutter opacity-50"></div>
        <div class="butterfly animate-float opacity-50"></div>
      </div>
    </div>

    <div class="space-x-4">
      <a href="/" class="btn-primary"> Follow the butterflies home </a>
      <a href="/shop" class="btn-secondary"> Browse our collection </a>
    </div>
  </div>
</div>
