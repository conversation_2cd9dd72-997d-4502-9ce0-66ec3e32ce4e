import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/database/db';
import { verifyJWT } from '$lib/server/auth/jwt';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ cookies }) => {
  const token = cookies.get('admin_token');
  
  if (!token) {
    return json({ error: 'Not authenticated' }, { status: 401 });
  }

  const payload = verifyJWT(token);
  
  if (!payload) {
    return json({ error: 'Invalid token' }, { status: 401 });
  }

  const orders = await prisma.order.findMany({
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true
        }
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  return json(orders);
};
