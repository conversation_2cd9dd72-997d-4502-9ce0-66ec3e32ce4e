<script lang="ts">
  import { onMount } from 'svelte';
  import type { Product } from '$lib/data/products';
  
  let products: Product[] = [];
  let loading = true;
  let showAddForm = false;
  let editingProduct: Product | null = null;
  
  let newProduct = {
    name: '',
    price: 0,
    image: '',
    gallery: [''],
    description: '',
    materials: '',
    category: 'rings' as const,
    featured: false,
    stock: 0
  };

  onMount(async () => {
    await loadProducts();
  });

  async function loadProducts() {
    try {
      const response = await fetch('/api/products');
      if (response.ok) {
        products = await response.json();
      }
    } catch (error) {
      console.error('Failed to load products:', error);
    } finally {
      loading = false;
    }
  }

  async function saveProduct() {
    try {
      const productData = {
        ...newProduct,
        gallery: newProduct.gallery.filter(url => url.trim() !== '')
      };

      const response = await fetch('/api/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        await loadProducts();
        resetForm();
        showAddForm = false;
      } else {
        alert('Failed to save product');
      }
    } catch (error) {
      console.error('Failed to save product:', error);
      alert('Failed to save product');
    }
  }

  async function updateProduct() {
    if (!editingProduct) return;

    try {
      const productData = {
        ...newProduct,
        gallery: newProduct.gallery.filter(url => url.trim() !== '')
      };

      const response = await fetch(`/api/products/${editingProduct.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        await loadProducts();
        resetForm();
        editingProduct = null;
      } else {
        alert('Failed to update product');
      }
    } catch (error) {
      console.error('Failed to update product:', error);
      alert('Failed to update product');
    }
  }

  async function deleteProduct(id: string) {
    if (!confirm('Are you sure you want to delete this product?')) return;

    try {
      const response = await fetch(`/api/products/${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await loadProducts();
      } else {
        alert('Failed to delete product');
      }
    } catch (error) {
      console.error('Failed to delete product:', error);
      alert('Failed to delete product');
    }
  }

  function editProduct(product: Product) {
    editingProduct = product;
    newProduct = {
      name: product.name,
      price: product.price,
      image: product.image,
      gallery: [...product.gallery],
      description: product.description,
      materials: product.materials,
      category: product.category,
      featured: product.featured,
      stock: product.stock || 0
    };
    showAddForm = true;
  }

  function resetForm() {
    newProduct = {
      name: '',
      price: 0,
      image: '',
      gallery: [''],
      description: '',
      materials: '',
      category: 'rings',
      featured: false,
      stock: 0
    };
    editingProduct = null;
  }

  function addGalleryImage() {
    newProduct.gallery = [...newProduct.gallery, ''];
  }

  function removeGalleryImage(index: number) {
    newProduct.gallery = newProduct.gallery.filter((_, i) => i !== index);
  }
</script>

<div class="px-4 py-6 sm:px-0">
  <div class="border-4 border-dashed border-gray-200 rounded-lg p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Products</h1>
      <button
        on:click={() => { showAddForm = true; resetForm(); }}
        class="bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700"
      >
        Add Product
      </button>
    </div>

    {#if showAddForm}
      <!-- Add/Edit Product Form -->
      <div class="bg-white p-6 rounded-lg shadow mb-6">
        <h2 class="text-xl font-semibold mb-4">
          {editingProduct ? 'Edit Product' : 'Add New Product'}
        </h2>
        
        <form on:submit|preventDefault={editingProduct ? updateProduct : saveProduct} class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                type="text"
                bind:value={newProduct.name}
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Price (₹)</label>
              <input
                type="number"
                bind:value={newProduct.price}
                required
                min="0"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                bind:value={newProduct.category}
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
              >
                <option value="rings">Rings</option>
                <option value="necklaces">Necklaces</option>
                <option value="bracelets">Bracelets</option>
                <option value="earrings">Earrings</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Stock</label>
              <input
                type="number"
                bind:value={newProduct.stock}
                required
                min="0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Main Image URL</label>
            <input
              type="url"
              bind:value={newProduct.image}
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Gallery Images</label>
            {#each newProduct.gallery as galleryUrl, index}
              <div class="flex gap-2 mb-2">
                <input
                  type="url"
                  bind:value={newProduct.gallery[index]}
                  placeholder="Image URL"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                />
                <button
                  type="button"
                  on:click={() => removeGalleryImage(index)}
                  class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                  Remove
                </button>
              </div>
            {/each}
            <button
              type="button"
              on:click={addGalleryImage}
              class="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Add Image
            </button>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              bind:value={newProduct.description}
              required
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            ></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Materials</label>
            <input
              type="text"
              bind:value={newProduct.materials}
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
            />
          </div>

          <div>
            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={newProduct.featured}
                class="mr-2"
              />
              <span class="text-sm font-medium text-gray-700">Featured Product</span>
            </label>
          </div>

          <div class="flex gap-2">
            <button
              type="submit"
              class="bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700"
            >
              {editingProduct ? 'Update' : 'Save'} Product
            </button>
            <button
              type="button"
              on:click={() => { showAddForm = false; resetForm(); }}
              class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    {/if}

    {#if loading}
      <div class="text-center py-8">
        <div class="animate-spin w-8 h-8 border-2 border-pink-soft border-t-transparent rounded-full mx-auto"></div>
        <p class="mt-2 text-gray-600">Loading products...</p>
      </div>
    {:else}
      <!-- Products Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Featured</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {#each products as product}
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <img class="h-10 w-10 rounded-lg object-cover" src={product.image} alt={product.name} />
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{product.name}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">{product.category}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{product.price}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.stock || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {product.featured ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    {product.featured ? 'Yes' : 'No'}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    on:click={() => editProduct(product)}
                    class="text-indigo-600 hover:text-indigo-900 mr-3"
                  >
                    Edit
                  </button>
                  <button
                    on:click={() => deleteProduct(product.id)}
                    class="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>
</div>
